@page
@model _Cashdisbursment_.Pages.Company.ApplicationsModel
@{
    ViewData["Title"] = "My Applications";
}

<div class="container py-2">
    <div class="page-header">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-1">
                            <i class="fas fa-file-alt me-2"></i>My Applications
                        </h1>
                        <p class="text-muted mb-0">Manage your fund applications</p>
                    </div>
                    <a asp-page="/Company/Applications/Create" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Application
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">Search</label>
                            <input type="text" name="search" value="@Model.SearchTerm" class="form-control" 
                                   placeholder="Search by description or purpose...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="pending" selected="@(Model.StatusFilter == "pending")">Pending</option>
                                <option value="approved" selected="@(Model.StatusFilter == "approved")">Approved</option>
                                <option value="rejected" selected="@(Model.StatusFilter == "rejected")">Rejected</option>
                                <option value="disbursed" selected="@(Model.StatusFilter == "disbursed")">Disbursed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date Range</label>
                            <select name="dateRange" class="form-select">
                                <option value="">All Time</option>
                                <option value="7" selected="@(Model.DateRange == "7")">Last 7 days</option>
                                <option value="30" selected="@(Model.DateRange == "30")">Last 30 days</option>
                                <option value="90" selected="@(Model.DateRange == "90")">Last 90 days</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications Accordion -->
    <div class="row">
        <div class="col-12">
            @{
                var pendingApps = Model.Applications.Where(a => a.Status == "Pending" && !a.IsDisbursed).ToList();
                var approvedApps = Model.Applications.Where(a => a.Status == "Approved").ToList();
                var rejectedApps = Model.Applications.Where(a => a.Status == "Rejected").ToList();
            }

            <div class="accordion applications-accordion" id="companyApplicationsAccordion">
                <!-- Pending Applications -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPending">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePending" aria-expanded="true" aria-controls="collapsePending">
                            <i class="fas fa-clock me-2 text-warning"></i>
                            Pending Applications
                            <span class="badge bg-warning ms-2">@pendingApps.Count</span>
                        </button>
                    </h2>
                    <div id="collapsePending" class="accordion-collapse collapse show" aria-labelledby="headingPending" data-bs-parent="#companyApplicationsAccordion">
                        <div class="accordion-body">
                            @if (pendingApps.Any())
                            {
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Description</th>
                                                <th>Purpose</th>
                                                <th>Amount</th>
                                                <th>Date Requested</th>
                                                <th>Approval Level</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var app in pendingApps)
                                            {
                                                <tr>
                                                    <td><strong>#@app.ApplicationID</strong></td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                            @app.Description
                                                        </div>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 150px;" title="@app.Purpose">
                                                    @app.Purpose
                                                </div>
                                            </td>
                                            <td>
                                                <strong>$@app.RequestedCash.ToString("N2")</strong>
                                            </td>
                                            <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (app.IsDisbursed)
                                                {
                                                    <span class="status-disbursed">
                                                        <i class="fas fa-money-bill-wave me-1"></i>Disbursed
                                                    </span>
                                                    @if (app.DateDisbursed.HasValue)
                                                    {
                                                        <small class="d-block text-muted">@app.DateDisbursed.Value.ToString("MMM dd, yyyy")</small>
                                                    }
                                                }
                                                else if (app.Status == "Approved")
                                                {
                                                    <span class="status-approved">
                                                        <i class="fas fa-check-circle me-1"></i>Approved
                                                    </span>
                                                }
                                                else if (app.Status == "Rejected")
                                                {
                                                    <span class="status-rejected">
                                                        <i class="fas fa-times-circle me-1"></i>Rejected
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="status-pending">
                                                        <i class="fas fa-clock me-1"></i>Pending
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @if (app.ApprovalLevel > 0)
                                                {
                                                    <span class="badge bg-info">Level @app.ApprovalLevel</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Not Started</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Company/Applications/Details" asp-route-id="@app.ApplicationID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @if (app.IsDisbursed)
                                                    {
                                                        <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@app.ApplicationID" 
                                                           class="btn btn-sm btn-outline-success" title="Submit Acquittal">
                                                            <i class="fas fa-receipt"></i>
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No pending applications</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Approved Applications -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingApproved">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseApproved" aria-expanded="false" aria-controls="collapseApproved">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            Approved Applications
                            <span class="badge bg-success ms-2">@approvedApps.Count</span>
                        </button>
                    </h2>
                    <div id="collapseApproved" class="accordion-collapse collapse" aria-labelledby="headingApproved" data-bs-parent="#companyApplicationsAccordion">
                        <div class="accordion-body">
                            @if (approvedApps.Any())
                            {
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Description</th>
                                                <th>Purpose</th>
                                                <th>Amount</th>
                                                <th>Date Requested</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var app in approvedApps)
                                            {
                                                <tr>
                                                    <td><strong>#@app.ApplicationID</strong></td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                            @app.Description
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 150px;" title="@app.Purpose">
                                                            @app.Purpose
                                                        </div>
                                                    </td>
                                                    <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                    <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                    <td>
                                                        @if (app.IsDisbursed)
                                                        {
                                                            <span class="status-indicator status-disbursed">Disbursed</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="status-indicator status-approved">Approved</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a asp-page="/Company/Applications/Details" asp-route-id="@app.ApplicationID"
                                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            @if (app.IsDisbursed)
                                                            {
                                                                <a asp-page="/Company/Acquittals/Create" asp-route-applicationId="@app.ApplicationID"
                                                                   class="btn btn-sm btn-outline-success" title="Submit Acquittal">
                                                                    <i class="fas fa-receipt"></i>
                                                                </a>
                                                            }
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No approved applications</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Rejected Applications -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingRejected">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseRejected" aria-expanded="false" aria-controls="collapseRejected">
                            <i class="fas fa-times-circle me-2 text-danger"></i>
                            Rejected Applications
                            <span class="badge bg-danger ms-2">@rejectedApps.Count</span>
                        </button>
                    </h2>
                    <div id="collapseRejected" class="accordion-collapse collapse" aria-labelledby="headingRejected" data-bs-parent="#companyApplicationsAccordion">
                        <div class="accordion-body">
                            @if (rejectedApps.Any())
                            {
                                <div class="table-container">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Description</th>
                                                <th>Purpose</th>
                                                <th>Amount</th>
                                                <th>Date Requested</th>
                                                <th>Comment</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var app in rejectedApps)
                                            {
                                                <tr>
                                                    <td><strong>#@app.ApplicationID</strong></td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 200px;" title="@app.Description">
                                                            @app.Description
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 150px;" title="@app.Purpose">
                                                            @app.Purpose
                                                        </div>
                                                    </td>
                                                    <td><strong>$@app.RequestedCash.ToString("N2")</strong></td>
                                                    <td>@app.DateRequested.ToString("MMM dd, yyyy")</td>
                                                    <td>
                                                        <div class="text-truncate" style="max-width: 150px;" title="@app.ApprovalComment">
                                                            @(string.IsNullOrEmpty(app.ApprovalComment) ? "No comment" : app.ApprovalComment)
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <a asp-page="/Company/Applications/Details" asp-route-id="@app.ApplicationID"
                                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-times-circle fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">No rejected applications</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            @if (!Model.Applications.Any())
            {
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-file-alt fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">No applications found</h5>
                        <p class="text-muted">You haven't submitted any applications yet.</p>
                        <a asp-page="/Company/Applications/Create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create Your First Application
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
