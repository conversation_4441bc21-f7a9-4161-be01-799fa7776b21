@page
@model _Cashdisbursment_.Pages.Admin.CompaniesModel
@{
    ViewData["Title"] = "Manage Companies";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-building me-2"></i>Manage Companies
                </h1>
                <a asp-page="/Admin/Companies/Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add Company
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.SuccessMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Filter and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Search</label>
                            <input type="text" name="search" value="@Model.SearchTerm" class="form-control" 
                                   placeholder="Search by company name or email...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Status</label>
                            <select name="status" class="form-select">
                                <option value="">All Status</option>
                                <option value="active" selected="@(Model.StatusFilter == "active")">Active</option>
                                <option value="inactive" selected="@(Model.StatusFilter == "inactive")">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Companies Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>Companies 
                        <span class="badge bg-secondary">@Model.Companies.Count</span>
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Companies.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Company Name</th>
                                        <th>Email</th>
                                        <th>Contact</th>
                                        <th>Address</th>
                                        <th>Status</th>
                                        <th>Applications</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var company in Model.Companies)
                                    {
                                        <tr>
                                            <td><strong>#@company.CompanyID</strong></td>
                                            <td>
                                                <div class="fw-bold">@company.Name</div>
                                            </td>
                                            <td>
                                                <a href="mailto:@company.Email" class="text-decoration-none">
                                                    @company.Email
                                                </a>
                                            </td>
                                            <td>@company.Contact</td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 150px;" title="@company.Address">
                                                    @company.Address
                                                </div>
                                            </td>
                                            <td>
                                                @if (company.Status)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <a asp-page="/Admin/Applications" asp-route-companyId="@company.CompanyID" 
                                                   class="btn btn-sm btn-outline-info">
                                                    View Applications
                                                </a>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-page="/Admin/Companies/Details" asp-route-id="@company.CompanyID" 
                                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-page="/Admin/Companies/Edit" asp-route-id="@company.CompanyID" 
                                                       class="btn btn-sm btn-outline-secondary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="post" asp-page-handler="ToggleStatus" class="d-inline">
                                                        <input type="hidden" name="companyId" value="@company.CompanyID" />
                                                        <button type="submit" 
                                                                class="btn btn-sm @(company.Status ? "btn-outline-warning" : "btn-outline-success")" 
                                                                title="@(company.Status ? "Deactivate" : "Activate")"
                                                                onclick="return confirm('Are you sure you want to @(company.Status ? "deactivate" : "activate") this company?')">
                                                            <i class="fas @(company.Status ? "fa-ban" : "fa-check")"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-building fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No companies found</h5>
                            <p class="text-muted">No companies match your search criteria.</p>
                            <a asp-page="/Admin/Companies/Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add First Company
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
