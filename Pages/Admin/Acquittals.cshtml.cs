using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace _Cashdisbursment_.Pages.Admin
{
    public class AcquittalsModel : PageModel
    {
        private readonly AcquittalService _acquittalService;
        private readonly CompanyService _companyService;

        public AcquittalsModel(AcquittalService acquittalService, CompanyService companyService)
        {
            _acquittalService = acquittalService;
            _companyService = companyService;
        }

        public User? CurrentUser { get; set; }
        public List<Acquittal> Acquittals { get; set; } = new();
        public List<AcquittalSubmission> AcquittalSubmissions { get; set; } = new();
        public List<Models.Company> Companies { get; set; } = new();
        public double TotalDisbursed { get; set; }
        public double TotalAcquitted { get; set; }

        // Filter properties
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public string? StatusFilter { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? CompanyFilter { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);

            if (CurrentUser == null || !AuthUtility.IsAdmin(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            try
            {
                // Load companies for filter dropdown
                Companies = await _companyService.GetAllCompaniesAsync();

                // Get all acquittals
                var allAcquittals = await _acquittalService.GetAllAcquittalsAsync();

                // Get all acquittal submissions
                foreach (var acquittal in allAcquittals)
                {
                    var submissions = await _acquittalService.GetSubmissionsByAcquittalIdAsync(acquittal.AcquittalID);
                    AcquittalSubmissions.AddRange(submissions);
                }

                // Apply filters
                Acquittals = FilterAcquittals(allAcquittals);

                // Calculate totals
                TotalDisbursed = allAcquittals.Sum(a => a.Application?.DisbursedCash ?? 0);
                TotalAcquitted = AcquittalSubmissions.Sum(s => s.Amount);

                return Page();
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while loading acquittals. Please try again.";
                return Page();
            }
        }

        private List<Acquittal> FilterAcquittals(List<Acquittal> acquittals)
        {
            var filtered = acquittals.AsQueryable();

            // Search filter
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                var searchLower = SearchTerm.ToLower();
                filtered = filtered.Where(a => 
                    (a.Application?.Company?.Name?.ToLower().Contains(searchLower) ?? false) ||
                    (a.Application?.Description?.ToLower().Contains(searchLower) ?? false));
            }

            // Company filter
            if (CompanyFilter.HasValue)
            {
                filtered = filtered.Where(a => a.Application?.CompanyID == CompanyFilter.Value);
            }

            // Status filter
            if (!string.IsNullOrEmpty(StatusFilter))
            {
                filtered = filtered.Where(a =>
                {
                    var acquittedAmount = AcquittalSubmissions
                        .Where(s => s.AcquittalID == a.AcquittalID)
                        .Sum(s => s.Amount);
                    var disbursedAmount = a.Application?.DisbursedCash ?? 0;
                    var outstanding = disbursedAmount - acquittedAmount;

                    return StatusFilter switch
                    {
                        "Complete" => outstanding <= 0,
                        "Partial" => outstanding > 0 && acquittedAmount > 0,
                        "Outstanding" => outstanding > 0 && acquittedAmount == 0,
                        _ => true
                    };
                });
            }

            return filtered.OrderByDescending(a => a.Application?.DateDisbursed).ToList();
        }
    }
}
