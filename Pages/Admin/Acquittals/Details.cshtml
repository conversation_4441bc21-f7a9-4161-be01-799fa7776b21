@page
@model _Cashdisbursment_.Pages.Admin.Acquittals.DetailsModel
@{
    ViewData["Title"] = "Acquittal Details";
}

<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-receipt me-2"></i>Acquittal Details - Application #@Model.Acquittal?.Application?.ApplicationID
                </h1>
                <a asp-page="/Admin/Acquittals" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Acquittals
                </a>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>@Model.ErrorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (Model.Acquittal != null)
    {
        <!-- Application Summary -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>Application Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Company:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.Company?.Name</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.Description</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Purpose:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.Purpose</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Requested Amount:</label>
                                    <p class="mb-0 text-primary fw-bold">$@((Model.Acquittal.Application?.RequestedCash ?? 0).ToString("N2"))</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Disbursed Amount:</label>
                                    <p class="mb-0 text-success fw-bold">$@((Model.Acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Date Disbursed:</label>
                                    <p class="mb-0">@Model.Acquittal.Application?.DateDisbursed?.ToString("MMM dd, yyyy")</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acquittal Summary -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-success">Total Disbursed</h5>
                        <h3 class="mb-0">$@((Model.Acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title text-info">Total Acquitted</h5>
                        <h3 class="mb-0">$@Model.TotalAcquitted.ToString("N2")</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title @(Model.OutstandingBalance > 0 ? "text-warning" : "text-success")">Outstanding</h5>
                        <h3 class="mb-0">$@Model.OutstandingBalance.ToString("N2")</h3>
                        @if (Model.OutstandingBalance <= 0)
                        {
                            <small class="text-success">✓ Complete</small>
                        }
                        else
                        {
                            <small class="text-warning">⚠ Pending</small>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Acquittal Submissions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            Acquittal Submissions 
                            <span class="badge bg-secondary">@Model.Submissions.Count</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (Model.Submissions.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Submitted By</th>
                                            <th>Document</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var submission in Model.Submissions.OrderByDescending(s => s.Date))
                                        {
                                            <tr>
                                                <td>
                                                    <div class="fw-bold">@submission.Date.ToString("MMM dd, yyyy")</div>
                                                    <small class="text-muted">@submission.Date.ToString("HH:mm")</small>
                                                </td>
                                                <td>
                                                    <div class="text-wrap" style="max-width: 300px;">
                                                        @submission.Description
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="fw-bold text-success">$@submission.Amount.ToString("N2")</span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">@submission.SubmittedBy</small>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(submission.PDFLocation))
                                                    {
                                                        <a href="@submission.PDFLocation" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-file-pdf me-1"></i>View
                                                        </a>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">No document</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- Summary Row -->
                            <div class="border-top pt-3 mt-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Total Submissions: @Model.Submissions.Count</strong>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Total Amount: $@Model.TotalAcquitted.ToString("N2")</strong>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        @if (Model.OutstandingBalance <= 0)
                                        {
                                            <span class="badge bg-success">Acquittal Complete</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-warning">$@Model.OutstandingBalance.ToString("N2") Outstanding</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Submissions Yet</h5>
                                <p class="text-muted">No acquittal submissions have been made for this application.</p>
                                <div class="alert alert-warning" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Outstanding Balance:</strong> $@((Model.Acquittal.Application?.DisbursedCash ?? 0).ToString("N2"))
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Compliance Status -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Compliance Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="progress mb-3">
                                    @{
                                        var disbursed = Model.Acquittal.Application?.DisbursedCash ?? 0;
                                        var percentage = disbursed > 0 ? (Model.TotalAcquitted / disbursed) * 100 : 0;
                                    }
                                    <div class="progress-bar @(percentage >= 100 ? "bg-success" : "bg-warning")" 
                                         role="progressbar" style="width: @Math.Min(percentage, 100)%">
                                        @percentage.ToString("F1")%
                                    </div>
                                </div>
                                <p class="mb-0">
                                    <strong>Acquittal Progress:</strong> 
                                    @percentage.ToString("F1")% of disbursed funds acquitted
                                </p>
                            </div>
                            <div class="col-md-6">
                                @if (Model.OutstandingBalance <= 0)
                                {
                                    <div class="alert alert-success mb-0" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <strong>Compliant:</strong> All disbursed funds have been properly acquitted.
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-warning mb-0" role="alert">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Pending:</strong> $@Model.OutstandingBalance.ToString("N2") still requires acquittal.
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
